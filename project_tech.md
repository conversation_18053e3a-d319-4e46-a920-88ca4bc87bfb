## 一、 腾讯体育：十亿级流量后台架构升级与稳定性建设 (2021.5 - 2023.5)

### 背景提要

-   **项目**: 体育接入层升级项目
-   **角色**: 负责人
-   **挑战**: PHP 单体应用演变为接入层，技术债严重，框架老、代码乱、性能差、运营难，面对日均十亿级流量和大型赛事洪峰流量，系统可用性面临巨大挑战。

### 面试模拟问答

#### 问：你提到主导了体育后台的微服务化改造，能谈谈你为什么决定做这次改造吗？主要的技术驱动力是什么？

> **回答**: 当然。这次改造可以说是被业务发展和技术现状"逼"出来的，核心驱动力就是解决当时面临的四大痛点：
>
> 1.  **框架老旧**：我们最初的后台是基于一个很老的PHP单体框架，不仅很难接入公司内部像天机阁、七彩石这样的新平台，而且安全问题频发，平均每个月都会爆出4个安全漏洞。
> 2.  **代码混乱**：系统堆积了十几年的代码，重复率高达4%，代码的平均圈复杂度达到了8.5，而且几乎没有单元测试覆盖。这使得任何修改都像是"在沼泽里走路"，风险极高。
> 3.  **性能瓶颈**：核心接口，比如比赛详情页，调用链路复杂且都是串行调用，导致单核QPS只能跑到200多，超时率达到3%。这在平时还能勉强支撑，但一到世界杯、NBA总决赛这样的高峰期就完全扛不住了。
> 4.  **运营困难**：我们没有现代化的CI/CD流水线，一次上线需要1到2个小时。更要命的是，可观测性体系（日志、监控、追踪）缺失，出了线上问题基本靠猜和本地断点调试，效率非常低下，也没有任何容灾降级能力。
>
> 综上，在体育APP7.0重构和向社区化转型的大背景下，老旧的接入层已经不堪重负。进行彻底的微服务化改造，是解决以上所有问题的唯一出路，目标就是实现**高可用、高扩展和高效率**。

#### 问：简历里提到你将架构分层为 API 网关、接口适配层和领域层。能详细解释一下这个架构吗？各层之间是如何交互的？

> **回答**: 这是我们改造的核心设计，目的是在彻底重构和保持业务稳定之间找到平衡。我们参考了业内主流的微服务分层思想，并结合体育的实际情况，设计了新的三层架构。
>
> 1.  **API 网关 (API Gateway)**: 这是所有流量的统一入口。我们对公司级的 **TAPISIX** 网关进行了二次开发。它剥离了所有非业务的通用功能，比如**身份认证、安全防护、请求路由、协议转换**，更重要的是，它成为了我们实现**全链路可观测、灰度发布和过载保护**等高级能力的统一入口。
>
> 2.  **接口适配层 (Adapter Layer)**: 这一层的核心使命是**适配与聚合**。它接收来自网关的请求，主要用 **Golang** 实现。它的职责是：
>     *   **适配多端**：将来自APP、Web、Pad等不同客户端的请求参数进行校验和组装。
>     *   **组合微服务**：它是轻薄的，主要负责并行调用下游的多个领域微服务，然后将返回结果聚合成前端需要的数据结构。业务逻辑会尽量下沉。
>     *   **保持单仓多产物**：为了提升复用和维护效率，所有适配层服务都在一个代码仓库里，但会按业务（如赛事、推荐）横向拆分成多个可独立部署的应用。
>
> 3.  **领域层 (Domain Layer)**: 这是真正的业务核心，负责处理核心、稳定的业务逻辑。我们将复杂的体育业务拆分为一个个独立的微服务，比如**用户中心、赛事中心、内容中心、推荐中心**等。每个服务都有自己独立的数据库和缓存，彼此通过 gRPC 进行通信。
>
> **交互流程**: 一个请求的典型路径是：`客户端 -> API网关 -> 接口适配层 -> 多个领域层服务 -> 接口适配层聚合数据 -> 返回客户端`。这套架构让我们能循序渐进地将老系统的逻辑一块块"掏空"并迁移到新服务中，保证了业务的平滑过渡。

#### 问：你刚才提到了领域驱动设计（DDD），能具体讲讲你是如何理解和应用它的吗？特别是在体育这个复杂的业务领域。

> **回答**: 好的。在我们看来，DDD 不是一个具体的框架，而是一套思想和方法论，核心是**让软件模型精准地反映业务领域的复杂性**。在体育项目中，我们应用 DDD 主要解决了两个核心问题：**一是如何拆解复杂的业务，二是如何让技术团队和产品、运营说"同一种语言"**。
>
> 具体来说，我们是这样实践的：
>
> 1.  **识别限界上下文 (Bounded Context)**: 这是 DDD 的核心战略。我们没有一开始就想着要拆成多少个微服务，而是先和产品、运营一起开会，梳理体育业务到底包含哪些相对独立的子领域。通过多轮讨论，我们识别出了几个核心的限界上下文，比如：
>     *   **赛事上下文 (Match Context)**: 负责所有与比赛相关的一切，比如赛程、赛果、对阵双方、实时数据等。
>     *   **用户上下文 (User Context)**: 管理用户信息、登录状态、会员权益等。
>     *   **内容上下文 (Content Context)**: 负责资讯、视频、集锦等内容的生产和管理。
>     *   **推荐上下文 (Recommendation Context)**: 负责为用户提供个性化的内容推荐。
>
>     **每个限界上下文最终就演变成了一个微服务**。比如"赛事上下文"就对应"赛事中心"服务。这个边界非常清晰，赛事中心就绝对不会处理用户登录的逻辑。
>
> 2.  **建立通用语言 (Ubiquitous Language)**: 在识别上下文的过程中，我们刻意统一了术语。比如，一场比赛，在产品、开发、测试的口中都统一叫做"赛事(Match)"，而不是"比赛(Game)"或"活动(Event)"。这个"赛事"包含了哪些状态(如"未开始"、"进行中"、"已结束")，它的唯一标识是什么，都形成了共识。这套通用语言最终沉淀在代码的模型、接口和数据库设计里，大大降低了沟通成本。
>
> 3.  **在上下文中进行战术设计**: 在每个限界上下文(微服务)内部，我们应用了战术设计模式。以"赛事中心"为例：
>     *   **聚合 (Aggregate)**: 我们将一场"赛事(Match)"定义为一个**聚合根 (Aggregate Root)**。所有与这场比赛相关的操作，比如更新比分、修改比赛状态，都必须通过这个聚合根来完成，保证了数据的一致性。你不能绕过"赛事"对象，直接去修改一个"得分"对象。
>     *   **实体 (Entity) 与值对象 (Value Object)**: 在"赛事"这个聚合里，"球队(Team)"可能是一个实体，因为它有唯一的ID，有自己的生命周期。而"比分(Score)"可能就是一个值对象，它只是描述赛事的一个属性，是可替换的。
>
> 通过这种方式，我们把复杂的体育业务，清晰地划分到了不同的领域模型中。这样做的好处是显而易见的：**第一，服务职责单一，内聚性高，易于维护和扩展。第二，团队可以按领域划分，每个团队都能独立地演进自己的服务，提升了开发效率。第三，清晰的领域模型也让新成员能更快地理解业务。** 所以，DDD 对我们项目的成功起到了至关重要的指导作用。

#### 问：在微服务拆分后，你们是如何保证跨服务的数据一致性的？比如，一个操作需要同时修改赛事数据和用户数据，这两个属于不同的微服务，你们是如何处理这种情况的？

> **回答**: 问得非常好，这确实是微服务架构中的经典难题。我们没有采用强一致性的分布式事务（如两阶段提交），因为它对系统性能和可用性的影响太大。我们遵循的是**最终一致性**的原则，主要通过**可靠事件模式**，基于**消息队列**来实现。
>
> 举个例子，比如"用户关注一场比赛"的操作：
>
> 1.  **触发操作**: 请求首先打到用户中心的"关注"接口。
> 2.  **本地事务与事件发布**: 用户中心在自己的数据库中，将"比赛ID"和"用户ID"的关注关系记录下来，同时，在同一个本地事务中，向一张"待发送事件表"里插入一条"用户已关注比赛"的事件。这样做能保证，只要关注成功，事件就一定能被记录下来。
> 3.  **可靠投递**: 我们有一个独立的、高可用的"事件发送服务"，它会准实时地扫描这张事件表，并将事件投递到 **RocketMQ** 这样的消息中间件里。投递成功后，再回来将事件表中的记录标记为"已发送"或删除。如果投递失败，它会不断重试。
> 4.  **下游消费**: 下游的"赛事中心"服务会订阅"用户已关注比赛"这个主题。当它收到消息后，会执行自己的业务逻辑，比如将对应赛事的"被关注数"加一。
> 5.  **补偿与幂等**: 为了应对下游消费失败的情况，我们会设计完善的重试和补偿机制。同时，下游服务的消费者必须是**幂等**的，确保即使收到重复的事件消息，也不会导致数据错乱（比如重复加一）。
>
> 通过这种基于**Saga模式（编排式）**的异步化方案，我们将跨服务的强一致性要求，解耦为多个服务内部的本地事务，极大地提升了系统的吞吐量和容错能力。

#### 问：你提到了建立了全链路可观测体系，能具体讲讲你们的技术栈和实践吗？比如，你们是如何将日志、指标和追踪关联起来，实现快速问题定位的？

> **回答**: 好的，我们的可观测体系（Observability）建设的核心是全面拥抱了 **OpenTelemetry** 这一行业标准。我们没有采用多个独立的、割裂的技术栈，而是通过 OpenTelemetry 实现了**日志、指标和追踪**三位一体的数据统一采集与关联，然后再将数据对接到后端不同的分析平台。
>
> 我们的实践可以分为三部分：
>
> 1.  **统一的信号采集**: 我们在所有微服务中，通过对tRPC框架的扩展，统一接入了 **OpenTelemetry SDK**。这意味着，我们的所有服务都使用同一套API和规范来产生和传递**Tracing (追踪)、Metrics (指标) 和 Logging (日志)** 信号。最关键的是，OpenTelemetry 会自动在这些信号间进行上下文传播，比如在所有日志和指标中自动注入 **TraceID**。
>
> 2.  **灵活的数据后端**: OpenTelemetry 的一大优势是与后端解耦。我们通过 OpenTelemetry Collector 收集数据后，根据信号类型的不同，将它们导出到最适合的专业系统中进行存储和可视化：
>     *   **追踪 (Tracing)**: 追踪数据我们导出到 **Jaeger**，用于调用链的分析和火焰图展示。
>     *   **指标 (Metrics)**: 核心业务和系统指标我们导出到 **Prometheus**，并使用 **Grafana** 创建多维度监控告警大盘。
>     *   **日志 (Logging)**: 带有 TraceID 的日志则统一发送到 **ELK** 技术栈进行聚合检索。
>
> **如何关联定位**: 这套体系的威力在于"关联"。当一个问题发生时，我们的SOP是这样的：
>
> *   **告警触发**: Grafana首先基于某个指标（比如"比赛详情接口"的错误率突增）触发告警。
> *   **查看链路**: 运维或开发同学收到告警后，可以从Grafana大盘上直接跳转到 Jaeger，查看在告警时间段内，有哪些请求的Trace是失败的。
> *   **锁定根因**: 在Jaeger的调用链火焰图中，我们可以清晰地看到是哪个服务的哪个环节耗时最长或返回了错误。
> *   **深入日志**: 拿到这个失败请求的TraceID后，我们再去Kibana里用这个TraceID一搜，就能瞬间拉出这个请求从进入网关开始，到经过所有微服务，所打印出来的全部相关日志。
>
> 通过这种以 **OpenTelemetry 为核心**的方式，我们把**指标、追踪、日志**三者天生就打通了。从发现问题（告警），到定位问题范围（追踪），再到分析问题根因（日志），整个过程非常流畅高效，基本实现了我们"问题定位10分钟内完成"的目标。

#### 问：你提到将单体拆分为了领域层，那在为这些新的领域微服务建立独立数据库时，你们是如何处理数据迁移的？这个过程是如何保证对线上业务无感的？

> **回答**: 数据迁移是我们这次重构中风险最高、也最谨慎的一环。我们严格遵循了业界成熟的"绞杀者模式" (Strangler Fig Pattern)，通过**增量、并行、可回退**的步骤，实现了平滑迁移。
>
> 整个过程可以分为几个阶段：
>
> 1.  **服务先行，数据库不动**: 在项目初期，我们新建的Go微服务在读写数据时，连接的还是老PHP项目背后的那个主数据库。这一步的目的是先将业务逻辑从老应用中剥离出来，并验证新服务的正确性，此时数据层面没有风险。
>
> 2.  **引入新库，数据双写**: 当新服务逻辑稳定后，我们会为这个微服务建立一个独立的新数据库。然后，我们在接口适配层（或者一个独立的代理层）启动**数据双写**。也就是说，所有写请求会同时写入老数据库和新数据库。这时，读请求依然只读老数据库。
>
> 3.  **历史数据迁移与校验**: 在双写的同时，我们会跑一个离线的脚本任务，将老库中的存量历史数据一次性地迁移到新库中。迁移完成后，我们还会写一个数据校验任务，持续地比对新老两个库中对应的数据，确保一致性。
>
> 4.  **灰度读切换**: 当我们通过校验任务确认新老数据库的数据在一段时间内（比如一周）完全一致后，就开始进行**读请求的切换**。我们会利用灰度发布系统，先切1%的流量去读新库，然后逐步加大比例到5%、20%，直到100%。这个过程中我们会密切监控业务指标，一旦发现问题，可以立刻切回读老库。
>
> 5.  **下线旧数据链路**: 当所有读写流量都切换到新服务和新库上，并稳定运行一段时间后，我们就可以安全地将老数据库的连接和双写逻辑代码下线了。
>
> 通过这种"三步走"的策略，每一步都是一个小的、可控的变化，并且都有回滚方案。这使得整个迁移过程对用户是完全透明的，保障了业务的连续性。

#### 问：你设计了全链路的容灾方案，实现了 99.99% 的可用性。能具体讲讲你是如何设计限流、降级和过载保护的吗？

> **回答**: 好的，99.99%的可用性是我们项目的核心目标，尤其体育业务有明显的流量潮汐效应，容灾建设是重中之重。我们的方案是一个立体的、贯穿全链路的体系，可以看作一个"服务容灾全景图"，核心是**限流、降级缓存和全链路过载保护**。
>
> 在**限流**方面，我们在网关层、服务级都做了常规的分布式限流。但我想重点讲讲我们是如何实现**全链路过载保护**的，这里我们解决了两个核心的难题：
>
> **第一个难题，是请求链路和页面链路的"成功率漏斗"问题。**
> 想象一下，一个用户的请求从APP到最终看到页面，要经过网关->适配层->多个微服务。如果每个服务都只顾自己，在自己快扛不住的时候就随机拒绝一部分请求，那么流量经过每一层都会被"漏掉"一部分，最终用户的整体成功率会非常低。
>
> *   **我们的方案是：制定全链路统一的拒绝策略。** 我们在API网关层开发了一个"请求优先级标记"插件。它可以根据**页面和接口的重要等级**来计算出一个0-255的优先级分数。这个分数会通过`priority`字段在整条调用链中透传。当任何一个下游服务需要过载保护时，它会优先拒绝优先级最低的请求。比如，我们会优先拒绝"观看记录上报"这类不影响页面框架的请求，从而把资源留给"比赛数据拉取"这样的核心请求，保证了核心链路的成功率。
>
> **第二个难题，是如何优化过载保护触发后的用户体验。**
> 如果触发了过载保护，就粗暴地给用户返回一个错误页面，体验会非常差。
>
> *   **我们的方案是：设计两层降级策略。**
>     1.  **微服务层降级**：当某个下游微服务（比如推荐服务）过载时，调用它的上游（接口适配层）会捕捉到过载信号，并触发降级逻辑。我们有几种降级方式：可以是从RPC缓存里读上一次的成功结果；也可以是调用一个独立的、逻辑更简单的兜底服务（比如用一个工程feed流来代替个性化推荐）；最差的情况才是把错误继续向上传递。
>     2.  **网关层降级**：如果连接口适配层本身都过载了，那说明系统压力已经非常大。这时，最外层的API网关会作为最后一道防线，直接读取它为这个接口缓存的数据（比如1分钟前的缓存）返回给用户。
>
> 通过**统一的优先级拒绝策略**和**分层的降级缓存**，我们从"被动地、随机地失败"变成了"主动地、有策略地丢卒保车"，在多次大型赛事中都成功地保障了核心页面的稳定。

#### 问：你提到了"多环境泳道"和"接口录制回放"，这些是如何提升研发效能的？能展开讲讲技术实现吗？

> **回答**: 这是我们解决微服务架构下测试环境复杂性问题的关键举措，我重点讲讲"多环境泳道"的实现，它基本上解决了我们测试环境的所有痛点。
>
> **1. 多环境泳道 (Swimlane Environments):**
>
> *   **痛点**: 以前我们的测试环境非常混乱。所有开发人员共用一个环境，经常互相覆盖；环境的路由配置也经常出错，导致30%的bug都是环境问题。更麻烦的是，像比赛状态变更（赛中、赛后）这种场景，测试数据很难构造，很多时候只能硬等线上数据或者发布后去线上验证。
>
> *   **实现思路**: 我们的核心思路是，**为每一个开发中的需求，自动创建一个逻辑隔离的、生命周期和需求绑定的"特性环境"**。
>     *   我们联动了公司的TAPD、工蜂和123（部署）平台，搭建了一个环境治理服务。当开发者基于某个需求（比如一个MiniFeature）创建特性分支时，会自动触发Git Hook，调用治理服务为这个需求创建一个专属的特性环境。
>     *   这个特性环境是"逻辑上"的。它只会部署这个需求改动的几个服务，其他没改动的服务，请求会默认路由到稳定的基线测试环境中去。
>     *   我们通过**流量染色**来实现泳道。客户端或开发者在请求头里带上特定的环境标识（如 `x-env-name=feature-xxx`），这个标识会在API网关被解析，然后通过tRPC框架的`selector-meta-env`字段在整条调用链中透传下去，确保请求始终在正确的泳道里流转。
>
> *   **技术难点与解决**: 在落地时我们遇到了两个比较棘手的问题：
>     *   **一是tRPC框架不支持`target`寻址的泳道**。我们的很多老服务用的是`target`寻址，而框架只支持`serviceName`寻址。我们的解决方案是和123平台合作，在他们创建北极星路由规则时，增加一条动态路由，让它能根据我们透传的`env`标识来匹配节点，如果匹配不到，再回退到基线环境。这个方案后来也被123平台采纳了。
>     *   **二是如何处理链路入口**。泳道的流量染色必须在链路第一跳就完成。我们利用了全量接入的TAPISIX网关，开发了一个环境治理插件，由它来统一解析请求里的环境标识，并向后续链路透传染色字段。
>
> *   **收益**: 这套方案上线后，彻底解决了环境污染和不稳定的问题。测试数据的构造成本也从原来的一两天降低到了分钟级。所有需求都实现了隔离开发和联调，环境类的bug降到了0。
>
> **2. 接口录制回放 (API Record & Replay):**
>
> *   **痛点**: 后端重构或修改接口后，如何验证其正确性？传统方式是QA手动测试，或者写大量的集成测试用例，成本很高。
> *   **实现**: 我们在 API 网关上开发了一个流量录制模块。当特定请求（比如带 `x-record=true` 的 Header）经过时，我们会将该请求的**入参**和线上真实服务的**返参**完整地记录下来，当开发人员重构完一个接口后，他可以在自动化测试平台上一键触发回放。平台会用录制下来的入参去请求新开发的接口，然后自动 **Diff** 新接口的返回和当时录制的真实返回。通过这种方式，我们用线上的真实流量构建了海量的回归测试用例，极大提升了重构的信心和效率。

## 二、 一点资讯：全网内容池与内容智能体系建设 (2019.4 - 2021.4)

### 背景提要

-   **项目**: 全网内容池、内容和作者特征挖掘
-   **角色**: 负责人
-   **挑战**: 作为内容平台，需要高效、海量地获取全网内容，并从中挖掘价值，以应对激烈的市场竞争。技术上需要面对大规模分布式爬虫系统、海量数据处理与智能分析等难题。

### 面试模拟问答

#### 问：你负责的"全网内容池"日均更新5000万+内容，这是一个非常大的体量。能介绍一下这个大规模分布式爬虫系统的核心架构吗？

> **回答**: 好的。要支撑如此大规模的抓取，我们没有选择像 Scrapy 这样的传统框架，因为我们评估后认为它在分布式调度和多爬虫统一管理方面比较薄弱。因此，我们基于 **Celery** 设计和搭建了一套**PaaS化的分布式爬虫平台**，核心理念是**调度与执行分离、配置化、服务化**。
>
> 它的核心是两层抽象：**细分任务** 和 **组合链路**。
>
> 1.  **细分任务 (Fine-grained Task)**: 这是我们系统中可以被独立调度和执行的最小原子操作。比如，"抓取某条微博的详情页"、"获取某篇文章的所有评论"都是一个细分任务。每个任务都有标准化的输入（如 `weibo_id`）、输出（解析后的字段）和机制（如超时、重试、监控）。
>
> 2.  **组合链路 (Chain)**: 运营或开发人员不需要写代码，他们通过我们的平台，可以将这些"细分任务"像积木一样灵活地拼接成一条"组合链路"，来满足具体的业务需求。例如，一个"监控某博主最新动态"的需求，就可以配置成一条链路：`抓取用户动态列表 -> 遍历列表 -> 抓取单条动态详情 -> 抓取评论`。这种方式将业务需求和爬虫代码完全解耦，极大提升了效率。
>
> 在这个体系下，我们的**调度中心**（基于 Airflow 定制）的核心职责不再是执行爬取，而是生成和管理这些"组合链路"任务，并把拆解后的"细分任务"扔到 **RabbitMQ** 消息队列里。下游是一个由大量 **Docker** 容器组成的、可弹性伸缩的 **Celery Worker 集群**来消费和执行这些任务。
>
> 同时，为了处理海量数据，我们还设计了一套分层的**存储方案**：
> *   **去重**：我们没有用简单的布隆过滤器，而是基于 Redis Hash 实现了一套自定义去重方案，通过分桶和哈希压缩，用约8GB内存就支撑了10亿级的带时间戳的去重记录。
> *   **存储**：抓取到的热数据会先进入 **MongoDB** 集群，它能支持高并发的读写和轻度查询。而全量数据和历史版本（如点赞数变化）则会归档到 **HBase** 中，并通过 **Elasticsearch** 提供全文检索能力。

#### 问：你的分布式爬虫系统听起来很灵活，但面对海量任务，你们是如何进行优先级调度的？比如，如何确保突发热点新闻的抓取优先级高于常规内容更新？

> **回答**: 这个问题提得很好，优先级调度是我们确保内容时效性的核心。我们的实现主要依赖**"多队列+动态优先级"**的策略。
>
> 1.  **多级任务队列**: 在 **RabbitMQ** 中，我们并没有使用单一的队列，而是设置了多个不同优先级的队列，比如 `high_priority_queue`、`normal_priority_queue` 和 `low_priority_queue`。下游的 Celery Worker 会被配置为优先从高优先级的队列中获取任务。
>
> 2.  **静态优先级分配**: 我们的调度中心（基于Airflow）在生成任务时，会根据任务源的属性，分配一个**静态优先级**。例如，像新华社、人民网这类权威新闻源，或者像微博热搜榜这样的高时效性渠道，它们的抓取任务天生就会被投递到 `high_priority_queue`。而一些常规的、更新频率较低的自媒体，则会被放入 `normal_priority_queue`。
>
> 3.  **动态优先级调整**: 更重要的是我们的**动态优先级机制**。我们有一个独立的"热点发现"服务，它会实时监控各大平台的热榜和社交媒体的趋势。当它发现一个突发事件或一个快速发酵的话题时，它会自动生成针对性的抓取任务（比如搜索特定关键词、抓取特定话题页），并将这些任务动态地、实时地注入到最高优先级的 `high_priority_queue` 中。
>
> 通过这种**静态与动态结合**的方式，我们既保证了日常抓取任务的平稳运行，又能对突发的热点新闻做出快速反应，确保了核心内容的时效性。

#### 问：简历中提到了突破公众号、小红书等主流平台的反爬，这是一个业界难题。你们具体用了哪些反反爬的策略和技术？

> **回答**: 是的，反爬对抗是我们工作的重点，它是一个持续动态博弈的过程。我们没有依赖单一技术，而是建立了一套立体的、组合式的服务和策略体系，基本可以分为三个层面：
>
> 1.  **基础资源层**：这是我们稳定抓取的基础。我们自建了两个核心资源池：
>     *   **智能代理IP池**：整合了多家供应商和自建的拨号VPS，建立了一个会自检测、自熔断、智能调度（比如贪婪模式与随机模式结合）的代理服务。
>     *   **大规模Cookie池**：针对需要登录的平台，我们有专门的程序进行模拟登录、验证码识别、养号等，来自动化地生产和维护一个高可用的Cookie池。
>
> 2.  **攻坚技术层**：针对主流平台越来越复杂的反爬手段，我们投入了很多精力在一些"硬核"技术上。
>     *   **AI验证码识别**：我们没有依赖传统的打码平台，而是自研和集成了基于深度学习的识别服务，能够处理包括滑动验证码、物体识别、文字点选在内的多种复杂验证码。
>     *   **定制化浏览器集群**：我们发现，像 Selenium/Puppeteer 这类标准的自动化工具，其指纹特征很容易被网站检测出来。为了从根源上解决这个问题，我们没有停留在使用现有工具的层面，而是走了更深的一条路：**基于 Chromium 和 CEF (Chromium Embedded Framework) 来构建我们自己的、高度定制化的浏览器环境**。通过直接修改 Chromium 源码和利用 CEF 提供的底层接口，我们可以深度篡改浏览器指纹，比如随机化或修改字体、插件列表、WebGL、Canvas 等关键信息。这使得我们的每一个爬虫实例在反爬系统看来，都是一个独特的、非自动化的真实浏览器，从而能有效规避当时主流的指- 纹检测。
>     *   **手机群控系统**：这是我们攻克App反爬的"杀手锏"。我们基于 **STF** 和 **atxserver2** 搭建了大规模的手机群控系统。通过这个系统，我们可以用 **Frida** 去Hook App的加解密方法来获取加密参数，或者用 **Auto.js** 编写脚本模拟真人点击操作，从而绕过App端的风控，获取最核心的数据。
>
> 3.  **策略与"玄学"层**：
>     *   **强拟人行为模拟**：破解了协议不代表就能高枕无忧。现在的App风控会分析用户行为。因此，我们不仅模拟数据请求，还会模拟大量的打点、日志上报等"非核心"请求，让我们的爬虫行为看起来更像一个真实的用户。
>     *   **养号**：对于账号体系要求很高的平台（如小红书、微博），我们会有一套完整的"养号"策略，模拟正常用户的浏览、点赞、评论行为，以维持账号的健康度和权重，防止被轻易封禁。
>
> 总之，我们是通过**资源、技术、策略**三位一体的纵深防御体系，来赢得这场反爬对抗的。

#### 问：你提到建立了内容和作者的分级体系，能否详细讲讲你们是如何利用全网数据来构建它，并最终转化为业务增长的？

> **回答**: 问得非常好。我们这个体系的核心思想，不是做一个内向的"数据仓库"，而是一个外向的**"全网情报雷达"**。我们的目标是捕捉外部世界的信号，并迅速应用到我们内部的内容生态中，核心解决两大问题：**一是"我们应该分发什么"，二是"我们应该引入谁"**。
>
> **1. 全网信号采集与处理**:
> 我们的系统架构就是为了高效地发现和处理外部信号而设计的。
> *   **信号源**: 我们的爬虫系统7x24小时监控着全网数百个高价值的信息源，可以分为几类：
>     *   **社交媒体**: 微博热搜、知乎热榜、抖音热门等。
>     *   **权威信源**: 主流新闻客户端的头条和Push。
>     *   **竞争对手**: 竞品的热门内容和分发动态。
> *   **处理链路**: 我们使用 **Flink** 对这些多源异构的信号进行实时的范式化清洗和事件提取，比如"某个关键词在微博的热度5分钟内上升了100%"。这些实时事件会触发下游的响应。同时，我们用 **Spark** 对全量信号进行天级的批处理，分析更宏观的跨平台趋势，比如"过去一周，'人工智能'主题在全网的热度持续走高"。
>
> **2. 应用一：以外部热度指导内容分发 (提升rctr)**
> 这是rctr提升18.4%的关键。我们设计了一个**"全网热度关联模型"**来解决内容冷启动和分发效率问题。
> *   **核心逻辑**: 当一篇新内容入库后，我们会迅速计算它与当前**全网实时热点事件**的关联强度。比如，如果一篇体育评论恰好与刚登上微博热搜的"某球星转会"事件高度相关，它就会被赋予一个极高的**"外部热力值"**。
> *   **赋能推荐**: 这个"外部热力值"会作为一个关键特征，直接输入到推荐系统的排序模型中。这意味着，一篇内容即便在我方平台是"零点击"的冷启动状态，但只要它"蹭"上了外部的热点，就能在初期获得大量的曝光机会，让潜在的爆款内容不会被埋没。我们用全网的智慧，弥补了自己平台早期信号的不足，这是提升分发效率的核心。
>
> **3. 应用二：以全网表现挖掘潜力作者 (赋能运营)**
> 这部分工作，是为我们的运营团队提供精准的"作者地图"，告诉他们应该去哪里"挖人"。
> *   **挖掘目标**: 我们不仅仅看作者在某个平台上的表现，而是更关注他们的**跨平台影响力和成长性**。
> *   **关键指标**: 我们会持续追踪目标作者在全网的动态，计算几个核心指标：
>     *   **粉丝增长加速度**: 该作者在主要平台近期的粉丝增长斜率。
>     *   **全网互动量**: 该作者的内容在所有平台的转、评、赞总和。
>     *   **内容垂直度与稀缺度**: 该作者是否专注于某个我方平台稀缺的垂直领域。
> *   **产出**: 我们会定期产出一个**"高潜力作者引入榜"**，运营团队可以根据这个榜单，按图索骥地去联系和签约那些在全网已经证明了自己，但在我方平台还未发力的优质创作者。这让我们的作者引入工作从"大海捞针"变成了"精准狙击"。
>
> 总结来说，我们的分级体系，本质上是一个**外部信号内部化**的系统。它通过技术手段，将全网的热点和趋势，转化为了我们内部可以执行的分发策略和运营动作，从而实现了数据驱动的业务增长。

#### 问：在内容分级体系中，你提到了"原创度"识别，这是一个难点。你们具体是如何计算一篇文章的原创度的？如何处理常见的"洗稿"和"伪原创"？

> **回答**：原创度识别确实非常复杂，单一算法很难奏效。我们的方案是一套组合拳，核心思想是"**快速召回 + 精准判断**"。
>
> 1.  **海量库去重与召回 (基于SimHash)**：对于一篇新入库的文章，我们首先要判断它是否是"旧闻"。要从百亿级的内容库中做比对，暴力匹配是不可能的。我们使用了 **SimHash** 算法，将每篇文章压缩成一个64位的"内容指纹"。查找相似文章，就变成了在海量指纹库中查找**汉明距离**小于等于N（比如3）的指纹，这个过程通过构建多重哈希索引可以做到毫秒级响应。这一步能快速筛出所有潜在的、高度相似的"嫌疑"文章。
>
> 2.  **文本相似度精算**：SimHash召回的只是"嫌疑犯"，我们需要进一步确认。对于召回的相似文章，我们会进行更精细的文本比对。我们结合了两种算法：一是基于 **TF-IDF** 计算两篇文章的**余弦相似度**，判断它们的关键词分布是否接近；二是计算它们的**最长公共子序列（LCS）**，来判断是否存在大段的原文照搬。
>
> 3.  **时间因子与来源权重**：技术比对之外，我们引入了两个关键业务因子。**一是发表时间**，同样的内容，发布时间更早的那个，原创概率显然更高。**二是来源权重**，我们会给不同网站/作者一个预设的"原创权重"，比如官方媒体的权重会高于普通自媒体。
>
> 4.  **模型综合判定**：最后，我们会将上述所有维度（SimHash距离、余弦相似度、LCS长度、时间差、来源权重等）作为特征，喂给一个 **XGBoost 分类模型**，由模型来综合判断，给出一个最终的"原创度得分"，而不是一个简单的"是/否"。
>
> 这套方案对"洗稿"（比如调换段落、替换同义词）和"拼接稿"都有比较好的识别效果，因为它不只看文本本身，而是多维度综合决策。

#### 问：关于技术选型有两个小问题想追问一下：1. 去重为什么不选择更节省内存的布隆过滤器？2. 对于点赞、评论数这类动态指标，用HBase的多版本来存，和专业的时序数据库（比如InfluxDB）相比，优劣在哪？

> **回答**：这两个问题都问到点子上了，是我们当时做技术选型时反复权衡过的地方。
>
> **1. 关于去重与布隆过滤器：**
>
> 我们当然深入评估过布隆过滤器，它在去重场景下确实是内存效率的王者。但我们最终放弃它的核心原因在于，它是一个"黑盒"，**只能回答'在或不在'，但无法提供额外信息，也不支持删除**。这和我们的业务需求有两点冲突：
>
> *   **需要知道抓取时间**：我们不仅想知道一个URL是否被抓过，更想知道**它上一次是什么时候被抓的**。这个时间戳信息对于判断内容时效性、控制重抓频率、分析站点更新规律等都至关重要。
> *   **需要支持记录过期**：很多内容是需要周期性重抓的，比如每小时更新一次的文章。这就要求我们的去重记录能够"过期"或被删除。标准的布隆过滤器不支持删除操作，这让我们的业务逻辑实现起来会非常复杂。
>
> 因此，我们选择基于Redis Hash的方案，虽然牺牲了一点内存，但换来了**存储额外信息（时间戳）**和**灵活删除/过期**这两个业务上必需的能力。我们认为这个 trade-off 是完全值得的。
>
> **2. 关于HBase与InfluxDB：**
>
> 你提的InfluxDB非常专业，坦白说，**如果现在让我重新设计这个模块，我很有可能会引入InfluxDB这样的时序数据库**。这确实是一个很好的反思点。
>
> 当时我们选择用HBase的多版本特性来存储动态指标，主要有两个原因：
>
> *   **技术栈收敛**：当时我们团队已经有了非常成熟的HBase运维经验和配套工具链，它已经是我们选定的全量存储方案。在此基础上，利用它的版本特性来"顺便"解决时序存储问题，可以**避免引入新的技术栈，降低系统的复杂度和维护成本**。
> *   **初期的查询模式简单**：项目初期，我们对这类时序数据的查询需求主要是"拉取某篇文章最新的N个点赞数版本"，用来做简单的趋势展示。HBase的多版本API可以很好地满足这个场景。
>
> 但是，随着业务发展，我们确实也发现了用HBase的弊端。当我们需要做更复杂的时序分析，比如按小时做窗口聚合、降采样等，HBase就力不从心了，必须把大量数据拉到计算层来处理。而InfluxDB在数据压缩模型、特别是它强大的内置聚合查询函数（如Flux语言）上，无疑是更专业、更高效的解决方案。
>
> 所以总结一下就是：用HBase是我们**在特定历史时期和技术栈背景下，做出的一个合理、务实的选择**。但从架构演进的角度看，**为专业的场景引入专业的工具，采用InfluxDB会是更优的方案**。 