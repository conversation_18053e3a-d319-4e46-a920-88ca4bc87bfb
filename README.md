# 面试准备资料索引

> 本文档为面试准备资料的总索引，帮助快速定位和查找相关问题和答案。

## 📋 文件结构概览

### 🎯 核心面试资料
- **[base.md](./base.md)** - 技术面试核心问题纲要（最重要）
- **[self_intro.md](./self_intro.md)** - 自我介绍模板
- **[project.md](./project.md)** - AI网文项目详细介绍
- **[project_tech.md](./project_tech.md)** - 腾讯体育技术项目详解

### 💻 算法准备
- **[leetcode.md](./leetcode.md)** - 10天LeetCode刷题计划
- **[algorithms.py](./algorithms.py)** - 常见算法实现代码

### 📄 简历相关
- **[resume.md](./resume.md)** - 简历内容（Markdown格式）
- **[resume.html](./resume.html)** - 简历网页版

### 🚀 扩展内容
- **[addon.md](./addon.md)** - 互动叙事等扩展项目介绍

---

## 🔍 快速查找指南

### 按面试类型查找

#### 技术面试
- **系统设计** → [base.md - 第七、八、九章](./base.md#七-分布式系统与微服务架构)
- **算法题** → [leetcode.md](./leetcode.md) + [algorithms.py](./algorithms.py)
- **项目经验** → [project_tech.md](./project_tech.md) + [project.md](./project.md)
- **基础知识** → [base.md - 第一到六章](./base.md#一-基础篇计算机网络与操作系统)

#### 业务面试
- **AIGC项目** → [project.md](./project.md)
- **管理经验** → [base.md - 第十章](./base.md#十-管理与领导力)
- **商业思维** → [base.md - 第十一章](./base.md#十一-商业思维与业务创新)
- **行业洞察** → [base.md - 第十二章](./base.md#十二-行业洞察与发展趋势)

#### HR面试
- **自我介绍** → [self_intro.md](./self_intro.md)
- **职业规划** → [base.md - 第十三章](./base.md#十三-职业发展与转型逻辑)
- **软技能** → [base.md - 第十四章](./base.md#十四-综合能力与软技能)

### 按技术领域查找

#### 后端技术
- **微服务架构** → [base.md - 分布式系统](./base.md) + [project_tech.md - 腾讯体育项目](./project_tech.md)
- **数据库** → [base.md - 数据库设计](./base.md)
- **缓存** → [base.md - 缓存策略](./base.md)
- **消息队列** → [base.md - 消息队列](./base.md)

#### AI/AIGC技术
- **AI内容生产** → [project.md - 完整方法论](./project.md)
- **大模型应用** → [project.md - Prompt工程](./project.md)
- **数据处理** → [base.md - 数据架构](./base.md)

#### 大数据技术
- **数据处理流水线** → [base.md - 数据架构](./base.md)
- **爬虫系统** → [base.md - 分布式爬虫](./base.md)
- **推荐系统** → [base.md - 推荐算法](./base.md)

### 按公司经历查找

#### 喜马拉雅（2023.6-至今）
- **核心项目** → [project.md - AI网文产线](./project.md)
- **管理经验** → [base.md - 管理与领导力](./base.md)
- **商业成果** → [project.md - 商业模式](./project.md)

#### 腾讯视频（2021.5-2023.5）
- **技术架构** → [project_tech.md - 微服务改造](./project_tech.md)
- **系统稳定性** → [project_tech.md - 可观测性](./project_tech.md)
- **高并发处理** → [project_tech.md - 性能优化](./project_tech.md)

#### 一点资讯（2019-2021）
- **数据处理** → [base.md - 数据架构](./base.md)
- **爬虫系统** → [base.md - 分布式爬虫](./base.md)
- **推荐算法** → [base.md - 推荐系统](./base.md)

---

## ⚡ 面试前快速复习清单

### 必背内容（5分钟）
1. **自我介绍** → [self_intro.md](./self_intro.md)
2. **核心项目亮点** → [project.md - 核心方法论](./project.md#一-核心理念与整体流程)
3. **技术栈总结** → [resume.md - 技能部分](./resume.md)

### 重点复习（15分钟）
1. **系统设计思路** → [base.md - 系统设计](./base.md)
2. **项目难点解决** → [project_tech.md - 技术挑战](./project_tech.md)
3. **算法常见题型** → [leetcode.md - 高频题目](./leetcode.md)

### 深度准备（30分钟）
1. **完整项目介绍** → [project.md](./project.md) + [project_tech.md](./project_tech.md)
2. **技术原理深挖** → [base.md - 全部章节](./base.md)
3. **行业思考** → [base.md - 行业洞察](./base.md)

---

## 📝 使用建议

### 面试前准备
1. **根据公司类型**选择重点复习内容
2. **根据岗位要求**调整技术深度
3. **准备3-5个**核心项目的详细介绍
4. **练习算法题**，保持手感

### 面试中使用
1. **快速定位**：使用本索引快速找到相关问题
2. **结构化回答**：参考文档中的回答思路
3. **举例说明**：结合具体项目经验回答
4. **适度展开**：根据面试官兴趣调整深度

### 持续更新
- 根据面试反馈更新和完善内容
- 添加新的项目经验和技术学习
- 定期复习和练习，保持熟练度

---

## 🎯 重点提醒

### 核心竞争优势
1. **AIGC领域的深度实践经验**
2. **从0到1构建技术体系的能力**
3. **技术与商业结合的综合能力**
4. **大规模系统的架构和稳定性经验**

### 面试策略
1. **突出差异化**：重点强调AIGC和AI应用经验
2. **数据说话**：用具体数字证明项目成果
3. **技术深度**：展现对底层技术的理解
4. **商业思维**：体现技术价值转化能力

### 注意事项
1. **保持谦逊**：承认不足，展现学习能力
2. **实事求是**：不夸大成果，诚实回答问题
3. **主动沟通**：适时提问，展现思考能力
4. **时间控制**：合理分配各部分回答时间

---

*最后更新时间：2024年*
*祝面试顺利！🚀*
